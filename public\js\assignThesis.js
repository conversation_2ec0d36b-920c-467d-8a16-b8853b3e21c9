// Assign Thesis JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Global variables
    let selectedStudent = null;
    let selectedTopic = null;
    let availableTopics = [];
    let temporaryAssignments = [];

    // DOM elements
    const studentSearchInput = document.getElementById('studentSearch');
    const searchBtn = document.getElementById('searchBtn');
    const studentResults = document.getElementById('studentResults');
    const studentList = document.getElementById('studentList');
    const selectedStudentSection = document.getElementById('selectedStudentSection');
    const selectedStudentInfo = document.getElementById('selectedStudentInfo');
    const changeStudentBtn = document.getElementById('changeStudentBtn');
    
    const topicSearchInput = document.getElementById('topicSearch');
    const availableTopicsList = document.getElementById('availableTopicsList');
    const assignBtn = document.getElementById('assignBtn');
    const assignmentStatus = document.getElementById('assignmentStatus');
    const selectedTopicPreview = document.getElementById('selectedTopicPreview');
    const selectedTopicInfo = document.getElementById('selectedTopicInfo');
    
    const temporaryAssignmentsContainer = document.getElementById('temporaryAssignments');
    const noAssignmentsMessage = document.getElementById('noAssignmentsMessage');

    // Modal elements
    const assignmentModal = new bootstrap.Modal(document.getElementById('assignmentModal'));
    const cancelAssignmentModal = new bootstrap.Modal(document.getElementById('cancelAssignmentModal'));
    const confirmAssignmentBtn = document.getElementById('confirmAssignmentBtn');
    const confirmCancelBtn = document.getElementById('confirmCancelBtn');
    const assignmentStudentInfo = document.getElementById('assignmentStudentInfo');
    const assignmentTopicInfo = document.getElementById('assignmentTopicInfo');
    const cancelAssignmentDetails = document.getElementById('cancelAssignmentDetails');

    // Initialize page
    init();

    function init() {
        loadAvailableTopics();
        loadTemporaryAssignments();
        setupEventListeners();
        updateAssignmentStatus();
    }

    function setupEventListeners() {
        // Student search
        searchBtn.addEventListener('click', searchStudents);
        studentSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchStudents();
            }
        });
        changeStudentBtn.addEventListener('click', clearSelectedStudent);

        // Topic search
        topicSearchInput.addEventListener('input', filterTopics);

        // Assignment
        assignBtn.addEventListener('click', showAssignmentModal);
        confirmAssignmentBtn.addEventListener('click', confirmAssignment);
        confirmCancelBtn.addEventListener('click', confirmCancellation);
    }

    // Student search functionality
    function searchStudents() {
        const query = studentSearchInput.value.trim();
        if (!query) {
            alert('Παρακαλώ εισάγετε ΑΜ ή όνομα φοιτητή');
            return;
        }

        // Mock API call - replace with actual API
        mockSearchStudents(query).then(students => {
            displayStudentResults(students);
        });
    }

    function displayStudentResults(students) {
        studentList.innerHTML = '';
        
        if (students.length === 0) {
            studentList.innerHTML = '<div class="list-group-item text-muted">Δεν βρέθηκαν φοιτητές</div>';
        } else {
            students.forEach(student => {
                const item = document.createElement('div');
                item.className = 'list-group-item list-group-item-action';
                item.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${student.name}</strong><br>
                            <small class="text-muted">ΑΜ: ${student.id}</small>
                        </div>
                        <button class="btn btn-outline-primary btn-sm" onclick="selectStudent('${student.id}')">
                            Επιλογή
                        </button>
                    </div>
                `;
                studentList.appendChild(item);
            });
        }
        
        studentResults.classList.remove('d-none');
    }

    window.selectStudent = function(studentId) {
        // Mock API call to get student details
        mockGetStudent(studentId).then(student => {
            selectedStudent = student;
            displaySelectedStudent();
            updateAssignmentStatus();
        });
    };

    function displaySelectedStudent() {
        selectedStudentInfo.innerHTML = `
            <strong>${selectedStudent.name}</strong><br>
            <small>ΑΜ: ${selectedStudent.id} | Email: ${selectedStudent.email}</small>
        `;
        selectedStudentSection.classList.remove('d-none');
        studentResults.classList.add('d-none');
        studentSearchInput.value = '';
    }

    function clearSelectedStudent() {
        selectedStudent = null;
        selectedStudentSection.classList.add('d-none');
        updateAssignmentStatus();
    }

    // Topic functionality
    function loadAvailableTopics() {
        // Mock API call - replace with actual API
        mockGetAvailableTopics().then(topics => {
            availableTopics = topics;
            displayTopics(topics);
        });
    }

    function displayTopics(topics) {
        availableTopicsList.innerHTML = '';
        
        if (topics.length === 0) {
            availableTopicsList.innerHTML = '<div class="list-group-item text-muted">Δεν υπάρχουν διαθέσιμα θέματα</div>';
        } else {
            topics.forEach(topic => {
                const item = document.createElement('div');
                item.className = 'list-group-item list-group-item-action';
                item.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${topic.title}</h6>
                            <p class="mb-1 small text-muted">${topic.summary}</p>
                            <small class="text-muted">Καθηγητής: ${topic.professor}</small>
                        </div>
                        <button class="btn btn-outline-success btn-sm ms-2" onclick="selectTopic('${topic.id}')">
                            Επιλογή
                        </button>
                    </div>
                `;
                availableTopicsList.appendChild(item);
            });
        }
    }

    function filterTopics() {
        const query = topicSearchInput.value.toLowerCase();
        const filtered = availableTopics.filter(topic => 
            topic.title.toLowerCase().includes(query) || 
            topic.summary.toLowerCase().includes(query)
        );
        displayTopics(filtered);
    }

    window.selectTopic = function(topicId) {
        selectedTopic = availableTopics.find(topic => topic.id === topicId);
        displaySelectedTopic();
        updateAssignmentStatus();
    };

    function displaySelectedTopic() {
        selectedTopicInfo.innerHTML = `
            <strong>${selectedTopic.title}</strong><br>
            <small class="text-muted">${selectedTopic.summary.substring(0, 100)}...</small>
        `;
        selectedTopicPreview.classList.remove('d-none');
    }

    function updateAssignmentStatus() {
        if (!selectedStudent && !selectedTopic) {
            assignmentStatus.innerHTML = '<div class="text-muted small"><p>Επιλέξτε φοιτητή και θέμα</p></div>';
            assignBtn.disabled = true;
        } else if (!selectedStudent) {
            assignmentStatus.innerHTML = '<div class="text-warning small"><p>Επιλέξτε φοιτητή</p></div>';
            assignBtn.disabled = true;
        } else if (!selectedTopic) {
            assignmentStatus.innerHTML = '<div class="text-warning small"><p>Επιλέξτε θέμα</p></div>';
            assignBtn.disabled = true;
        } else {
            assignmentStatus.innerHTML = '<div class="text-success small"><p>Έτοιμο για ανάθεση!</p></div>';
            assignBtn.disabled = false;
        }
    }

    // Assignment functionality
    function showAssignmentModal() {
        if (!selectedStudent || !selectedTopic) return;

        assignmentStudentInfo.innerHTML = `
            <strong>${selectedStudent.name}</strong><br>
            <small>ΑΜ: ${selectedStudent.id}</small><br>
            <small>Email: ${selectedStudent.email}</small>
        `;

        assignmentTopicInfo.innerHTML = `
            <strong>${selectedTopic.title}</strong><br>
            <small class="text-muted">${selectedTopic.summary}</small><br>
            <small>Καθηγητής: ${selectedTopic.professor}</small>
        `;

        assignmentModal.show();
    }

    function confirmAssignment() {
        // Mock API call - replace with actual API
        mockAssignTopic(selectedStudent.id, selectedTopic.id).then(result => {
            if (result.success) {
                alert('Η ανάθεση ολοκληρώθηκε επιτυχώς!');
                
                // Reset form
                selectedStudent = null;
                selectedTopic = null;
                clearSelectedStudent();
                selectedTopicPreview.classList.add('d-none');
                updateAssignmentStatus();
                
                // Reload data
                loadAvailableTopics();
                loadTemporaryAssignments();
                
                assignmentModal.hide();
            } else {
                alert('Σφάλμα κατά την ανάθεση: ' + result.message);
            }
        });
    }

    // Temporary assignments functionality
    function loadTemporaryAssignments() {
        // Mock API call - replace with actual API
        mockGetTemporaryAssignments().then(assignments => {
            temporaryAssignments = assignments;
            displayTemporaryAssignments();
        });
    }

    function displayTemporaryAssignments() {
        temporaryAssignmentsContainer.innerHTML = '';
        
        if (temporaryAssignments.length === 0) {
            noAssignmentsMessage.classList.remove('d-none');
        } else {
            noAssignmentsMessage.classList.add('d-none');
            
            temporaryAssignments.forEach(assignment => {
                const item = document.createElement('div');
                item.className = 'list-group-item';
                item.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">${assignment.topicTitle}</h6>
                            <p class="mb-1">Φοιτητής: <strong>${assignment.studentName}</strong> (ΑΜ: ${assignment.studentId})</p>
                            <small class="text-muted">Ημερομηνία ανάθεσης: ${assignment.assignmentDate}</small>
                        </div>
                        <button class="btn btn-outline-danger btn-sm" onclick="showCancelModal('${assignment.id}')">
                            Ακύρωση
                        </button>
                    </div>
                `;
                temporaryAssignmentsContainer.appendChild(item);
            });
        }
    }

    window.showCancelModal = function(assignmentId) {
        const assignment = temporaryAssignments.find(a => a.id === assignmentId);
        
        cancelAssignmentDetails.innerHTML = `
            <strong>${assignment.topicTitle}</strong><br>
            Φοιτητής: ${assignment.studentName} (ΑΜ: ${assignment.studentId})<br>
            <small class="text-muted">Ανατέθηκε: ${assignment.assignmentDate}</small>
        `;
        
        confirmCancelBtn.onclick = () => cancelAssignment(assignmentId);
        cancelAssignmentModal.show();
    };

    function cancelAssignment(assignmentId) {
        // Mock API call - replace with actual API
        mockCancelAssignment(assignmentId).then(result => {
            if (result.success) {
                alert('Η ανάθεση ακυρώθηκε επιτυχώς!');
                loadTemporaryAssignments();
                loadAvailableTopics();
                cancelAssignmentModal.hide();
            } else {
                alert('Σφάλμα κατά την ακύρωση: ' + result.message);
            }
        });
    }

    // Mock API functions - replace with actual API calls
    function mockSearchStudents(query) {
        return new Promise(resolve => {
            setTimeout(() => {
                const mockStudents = [
                    { id: '1001', name: 'Γιάννης Παπαδόπουλος', email: '<EMAIL>' },
                    { id: '1002', name: 'Μαρία Κωνσταντίνου', email: '<EMAIL>' },
                    { id: '1003', name: 'Νίκος Αντωνίου', email: '<EMAIL>' }
                ];
                const filtered = mockStudents.filter(s => 
                    s.id.includes(query) || s.name.toLowerCase().includes(query.toLowerCase())
                );
                resolve(filtered);
            }, 500);
        });
    }

    function mockGetStudent(studentId) {
        return new Promise(resolve => {
            setTimeout(() => {
                const mockStudents = {
                    '1001': { id: '1001', name: 'Γιάννης Παπαδόπουλος', email: '<EMAIL>' },
                    '1002': { id: '1002', name: 'Μαρία Κωνσταντίνου', email: '<EMAIL>' },
                    '1003': { id: '1003', name: 'Νίκος Αντωνίου', email: '<EMAIL>' }
                };
                resolve(mockStudents[studentId]);
            }, 200);
        });
    }

    function mockGetAvailableTopics() {
        return new Promise(resolve => {
            setTimeout(() => {
                const mockTopics = [
                    {
                        id: 'topic1',
                        title: 'Ανάπτυξη Web Εφαρμογής με React',
                        summary: 'Δημιουργία σύγχρονης web εφαρμογής χρησιμοποιώντας React και Node.js',
                        professor: 'Δρ. Αντώνιος Παπαδάκης'
                    },
                    {
                        id: 'topic2',
                        title: 'Μηχανική Μάθηση για Ανάλυση Δεδομένων',
                        summary: 'Εφαρμογή αλγορίθμων μηχανικής μάθησης για ανάλυση μεγάλων δεδομένων',
                        professor: 'Δρ. Μαρία Γεωργίου'
                    },
                    {
                        id: 'topic3',
                        title: 'Ασφάλεια Δικτύων και Κρυπτογραφία',
                        summary: 'Μελέτη και υλοποίηση πρωτοκόλλων ασφαλείας για δίκτυα υπολογιστών',
                        professor: 'Δρ. Κωνσταντίνος Λάμπρου'
                    }
                ];
                resolve(mockTopics);
            }, 300);
        });
    }

    function mockGetTemporaryAssignments() {
        return new Promise(resolve => {
            setTimeout(() => {
                const mockAssignments = [
                    {
                        id: 'assign1',
                        topicTitle: 'Ανάπτυξη Mobile App',
                        studentName: 'Ελένη Μιχαήλ',
                        studentId: '1004',
                        assignmentDate: '2025-01-15'
                    }
                ];
                resolve(mockAssignments);
            }, 300);
        });
    }

    function mockAssignTopic(studentId, topicId) {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({ success: true });
            }, 500);
        });
    }

    function mockCancelAssignment(assignmentId) {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({ success: true });
            }, 500);
        });
    }
});
