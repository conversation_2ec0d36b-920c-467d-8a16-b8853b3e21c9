<!doctype html>
<html lang="el">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="/stylesheets/style.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous" />
    <title>Assign Thesis Topic</title>
</head>

<body id="body" class="d-flex flex-column min-vh-100">
    <div id="navbar"></div>

    <main class="flex-grow-1">
        <h2 class="display-4 text-center mt-5 p-5">Ανάθεση Θέματος Διπλωματικής</h2>

        <div class="container">
            <!-- Student Search Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">Αναζήτηση Φοιτητή</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="studentSearch" class="form-label">Αναζήτηση με ΑΜ ή Όνομα</label>
                            <input type="text" class="form-control" id="studentSearch"
                                   placeholder="Εισάγετε ΑΜ ή όνομα φοιτητή...">
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button id="searchBtn" class="btn btn-primary me-2">Αναζήτηση</button>
                            <button id="clearSearchBtn" class="btn btn-outline-secondary">Καθαρισμός</button>
                        </div>
                    </div>

                    <!-- Search Results -->
                    <div id="studentResults" class="mt-3 d-none">
                        <h6>Αποτελέσματα Αναζήτησης:</h6>
                        <div id="studentList" class="list-group">
                            <!-- Student search results will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Selected Student Section -->
            <div id="selectedStudentSection" class="card mb-4 d-none">
                <div class="card-header">
                    <h4 class="mb-0">Επιλεγμένος Φοιτητής</h4>
                </div>
                <div class="card-body">
                    <div id="selectedStudentInfo">
                        <!-- Selected student info will be displayed here -->
                    </div>
                    <button id="changeStudentBtn" class="btn btn-outline-primary mt-2">Αλλαγή Φοιτητή</button>
                </div>
            </div>

            <!-- Available Topics Section -->
            <div id="availableTopicsSection" class="card mb-4 d-none">
                <div class="card-header">
                    <h4 class="mb-0">Διαθέσιμα Θέματα</h4>
                </div>
                <div class="card-body">
                    <div id="topicsList" class="list-group">
                        <!-- Available topics will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Current Assignments Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">Τρέχουσες Αναθέσεις (Προσωρινές)</h4>
                </div>
                <div class="card-body">
                    <p class="text-muted">Θέματα που έχουν ανατεθεί προσωρινά και αναμένουν έγκριση από την Τριμελή Επιτροπή</p>
                    <div id="currentAssignments" class="list-group">
                        <!-- Current temporary assignments will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Assignment Confirmation Modal -->
        <div class="modal fade" id="assignmentModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Επιβεβαίωση Ανάθεσης</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Θέλετε να αναθέσετε το θέμα:</p>
                        <div id="assignmentDetails" class="border p-3 bg-light rounded">
                            <!-- Assignment details will be shown here -->
                        </div>
                        <div class="alert alert-info mt-3">
                            <strong>Σημείωση:</strong> Η ανάθεση θα είναι προσωρινή μέχρι να εγκριθεί από την Τριμελή Επιτροπή.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Άκυρο</button>
                        <button id="confirmAssignmentBtn" type="button" class="btn btn-primary">Επιβεβαίωση Ανάθεσης</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cancel Assignment Modal -->
        <div class="modal fade" id="cancelAssignmentModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Ακύρωση Ανάθεσης</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Θέλετε να ακυρώσετε την ανάθεση του θέματος:</p>
                        <div id="cancelAssignmentDetails" class="border p-3 bg-light rounded">
                            <!-- Assignment details will be shown here -->
                        </div>
                        <div class="alert alert-warning mt-3">
                            <strong>Προσοχή:</strong> Η ακύρωση θα καταστήσει το θέμα ξανά διαθέσιμο για ανάθεση.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Άκυρο</button>
                        <button id="confirmCancelBtn" type="button" class="btn btn-danger">Ακύρωση Ανάθεσης</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <div id="footer"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous">
        </script>
    <script src="/js/loadPartials.js"></script>
    <script src="/js/assignThesis.js"></script>
</body>

</html>