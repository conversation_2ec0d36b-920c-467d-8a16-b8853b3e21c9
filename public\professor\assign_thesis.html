<!doctype html>
<html lang="el">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="/stylesheets/style.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous" />
    <title>Assign Thesis Topic</title>
</head>

<body id="body" class="d-flex flex-column min-vh-100">
    <div id="navbar"></div>

    <main class="flex-grow-1">
        <h2 class="display-4 text-center mt-5 p-5">Ανάθεση Θέματος Διπλωματικής</h2>

        <div class="container">
            <!-- PART 1: ASSIGNMENT FUNCTIONALITY -->
            <div class="row">
                <!-- Student Search Section -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h4 class="mb-0">1. Αναζήτηση Φοιτητή</h4>
                        </div>
                        <div class="card-body">
                            <label for="studentSearch" class="form-label">Αναζήτηση με ΑΜ ή Όνομα</label>
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" id="studentSearch"
                                       placeholder="Εισάγετε ΑΜ ή όνομα φοιτητή...">
                                <button id="searchBtn" class="btn btn-primary">Αναζήτηση</button>
                            </div>

                            <!-- Search Results -->
                            <div id="studentResults" class="d-none">
                                <h6>Αποτελέσματα:</h6>
                                <div id="studentList" class="list-group">
                                    <!-- Student search results will be populated here -->
                                </div>
                            </div>

                            <!-- Selected Student -->
                            <div id="selectedStudentSection" class="d-none">
                                <div class="alert alert-success">
                                    <h6 class="alert-heading">Επιλεγμένος Φοιτητής:</h6>
                                    <div id="selectedStudentInfo">
                                        <!-- Selected student info will be displayed here -->
                                    </div>
                                    <button id="changeStudentBtn" class="btn btn-outline-success btn-sm mt-2">Αλλαγή</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Available Topics Section -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h4 class="mb-0">2. Διαθέσιμα Θέματα</h4>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <input type="text" class="form-control" id="topicSearch"
                                       placeholder="Αναζήτηση θέματος...">
                            </div>

                            <div id="availableTopicsList" class="list-group" style="max-height: 400px; overflow-y: auto;">
                                <!-- Available topics will be populated here -->
                            </div>

                            <div class="mt-3">
                                <small class="text-muted">
                                    Επιλέξτε ένα διαθέσιμο θέμα για να το αναθέσετε στον φοιτητή
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PART 2: CANCELLATION FUNCTIONALITY -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">3. Διαχείριση Προσωρινών Αναθέσεων</h4>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">
                        Θέματα που έχετε αναθέσει προσωρινά και αναμένουν έγκριση από την Τριμελή Επιτροπή.
                        Μπορείτε να ακυρώσετε οποιαδήποτε ανάθεση πριν την οριστικοποίηση.
                    </p>

                    <div id="temporaryAssignments" class="list-group">
                        <!-- Temporary assignments will be populated here -->
                    </div>

                    <div id="noAssignmentsMessage" class="text-center text-muted py-4">
                        <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                        <p class="mt-2">Δεν υπάρχουν προσωρινές αναθέσεις</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Assignment Confirmation Modal -->
        <div class="modal fade" id="assignmentModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Επιβεβαίωση Ανάθεσης Θέματος</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">Φοιτητής:</h6>
                                <div id="assignmentStudentInfo" class="border p-3 bg-light rounded mb-3">
                                    <!-- Student info will be shown here -->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">Θέμα Διπλωματικής:</h6>
                                <div id="assignmentTopicInfo" class="border p-3 bg-light rounded mb-3">
                                    <!-- Topic info will be shown here -->
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <strong>Σημείωση:</strong> Η ανάθεση θα είναι προσωρινή μέχρι να εγκριθεί από την Τριμελή Επιτροπή.
                            Μπορείτε να την ακυρώσετε οποιαδήποτε στιγμή πριν την οριστικοποίηση.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Άκυρο</button>
                        <button id="confirmAssignmentBtn" type="button" class="btn btn-primary">
                            Επιβεβαίωση Ανάθεσης
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cancel Assignment Modal -->
        <div class="modal fade" id="cancelAssignmentModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Ακύρωση Ανάθεσης</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Θέλετε να ακυρώσετε την ανάθεση του θέματος:</p>
                        <div id="cancelAssignmentDetails" class="border p-3 bg-light rounded">
                            <!-- Assignment details will be shown here -->
                        </div>
                        <div class="alert alert-warning mt-3">
                            <strong>Προσοχή:</strong> Η ακύρωση θα καταστήσει το θέμα ξανά διαθέσιμο για ανάθεση.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Άκυρο</button>
                        <button id="confirmCancelBtn" type="button" class="btn btn-danger">Ακύρωση Ανάθεσης</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <div id="footer"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous">
        </script>
    <script src="/js/loadPartials.js"></script>
    <script src="/js/assignThesis.js"></script>
</body>

</html>